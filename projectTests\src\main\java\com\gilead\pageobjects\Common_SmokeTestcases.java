package com.gilead.pageobjects;

import org.openqa.selenium.By;

/**
 * UI Map for Common Objects
 */
public class Common_SmokeTestcases {

	// DX Somke Test cases New
	public By verifyLinks, confirmationPopup, acceptDeclineBtn, componetentExists, preCondition, subMenu, menu,
			registerLinks, pdf;

	/**
	 * Constructor to handle multiple locators using one parameter
	 * 
	 * @param strLabel - Parameters for xpath locator
	 */

	public Common_SmokeTestcases(String strLabel) {

		// DX Smoke Test cases
		verifyLinks = By.xpath("(//li[@class='menu-level-1']//*[contains(text(),'" + strLabel
				+ "')])|(//a[@class='ui-tabs-anchor'][contains(text(),'" + strLabel
				+ "')])|(//a[@class='nav-link ']//*[contains(text(),'" + strLabel 
				+ "')])|(//div[contains(@class,'nav')]//a[contains(.,'" + strLabel + "')])|//input[contains(@name,'"+ strLabel +"')]|(//a[contains(@class,'menu-level-2') and contains(.,'"+ strLabel + "')])|//a[text()='"+ strLabel +"']");
		confirmationPopup = By.xpath("//a[contains(normalize-space(.), '" + strLabel + "')]");
		acceptDeclineBtn = By.xpath("//a[contains(text(), '" + strLabel + "')]");
		componetentExists = By.xpath("(//div[contains(@class,'row justify-content-center')]//*[contains(text(),'"
				+ strLabel + "')])|(//label[contains(text(),'" + strLabel
				+ "')])|(//div[@class='hero-banner-content']//a[@href='" + strLabel + "'])");
		preCondition = By.xpath("//*[contains(text(),'" + strLabel + "')]");
		menu = By.xpath("//a[contains(@id,'navbarDropdownMenuLink')]//*[contains(text(),'" + strLabel + "')]");
		subMenu = By.xpath("//a[@class='dropdown-item ']//*[contains(text(),'" + strLabel + "')]");
		registerLinks = By.xpath("//div[contains(@class,'card-wrapper')]//*[contains(text(),'" + strLabel + "')]");
		pdf = By.xpath("//a[contains(.,'" + strLabel + "')]");
	}
}
