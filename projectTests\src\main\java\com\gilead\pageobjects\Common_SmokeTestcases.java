package com.gilead.pageobjects;

import org.openqa.selenium.By;

/**
 * UI Map for Common Objects - DX Smoke Test Cases
 *
 * This class provides locators for various UI elements used in smoke testing
 * across different pages and components of the Digital Experience application.
 */
public class Common_SmokeTestcases {

	// ========================================
	// PAGE ELEMENT LOCATORS
	// ========================================

	/** Locator for verifying links across different page structures */
	public By verifyLinks;

	/** Locator for confirmation popup elements */
	public By confirmationPopup;

	/** Locator for accept/decline buttons */
	public By acceptDeclineBtn;

	/** Locator for component existence validation */
	public By componetentExists;

	/** Locator for pre-condition elements */
	public By preCondition;

	/** Locator for sub-menu elements */
	public By subMenu;

	/** Locator for main menu elements */
	public By menu;

	/** Locator for registration links */
	public By registerLinks;

	/** Locator for PDF elements */
	public By pdf;

	// ========================================
	// CONSTRUCTOR
	// ========================================

	/**
	 * Constructor to initialize locators with dynamic parameter
	 *
	 * @param strLabel - Dynamic parameter for xpath locator construction
	 */
	public Common_SmokeTestcases(String strLabel) {
		initializeLocators(strLabel);
	}

	// ========================================
	// PRIVATE METHODS
	// ========================================

	/**
	 * Initialize all locators with the provided label parameter
	 *
	 * @param strLabel - Dynamic parameter for xpath construction
	 */
	private void initializeLocators(String strLabel) {

		// Verify Links - Multiple xpath patterns for link verification
		verifyLinks = By.xpath(
			"(//li[@class='menu-level-1']//*[contains(text(),'" + strLabel + "')])" +
			"|(//a[@class='ui-tabs-anchor'][contains(text(),'" + strLabel + "')])" +
			"|(//a[@class='nav-link ']//*[contains(text(),'" + strLabel + "')])" +
			"|(//div[contains(@class,'nav')]//a[contains(.,'" + strLabel + "')])" +
			"|//input[contains(@name,'" + strLabel + "')]" +
			"|(//a[contains(@class,'menu-level-2') and contains(.,'" + strLabel + "')])" +
			"|//a[text()='" + strLabel + "']"+
			"|(//*[@class='card']//a[contains(text(),'" + strLabel + "')])"
		);

		// Confirmation Popup - Normalized space text matching
		confirmationPopup = By.xpath(
			"//a[contains(normalize-space(.), '" + strLabel + "')]"
		);

		// Accept/Decline Buttons - Simple text matching
		acceptDeclineBtn = By.xpath(
			"//a[contains(text(), '" + strLabel + "')]"
		);

		// Component Existence - Multiple patterns for component validation
		componetentExists = By.xpath(
			"(//div[contains(@class,'row justify-content-center')]//*[contains(text(),'" + strLabel + "')])" +
			"|(//label[contains(text(),'" + strLabel + "')])" +
			"|(//div[@class='hero-banner-content']//a[@href='" + strLabel + "'])" +
			"|//h3[contains(.,'" + strLabel + "')]" +
			"|//h2[contains(.,'" + strLabel + "')]"
		);

		// Pre-condition - Generic text matching
		preCondition = By.xpath(
			"//*[contains(text(),'" + strLabel + "')]"
		);

		// Main Menu - Navbar dropdown menu links
		menu = By.xpath(
			"//a[contains(@id,'navbarDropdownMenuLink')]//*[contains(text(),'" + strLabel + "')]"
		);

		// Sub Menu - Dropdown item links
		subMenu = By.xpath(
			"//a[@class='dropdown-item ']//*[contains(text(),'" + strLabel + "')]"
		);

		// Register Links - Card wrapper elements
		registerLinks = By.xpath(
			"//div[contains(@class,'card-wrapper')]//*[contains(text(),'" + strLabel + "')]"
		);

		// PDF Elements - Generic anchor tag matching
		pdf = By.xpath(
			"//a[contains(.,'" + strLabel + "')]"
		);
	}
}
