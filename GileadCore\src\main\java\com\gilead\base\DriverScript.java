package com.gilead.base;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.mail.BodyPart;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.Platform;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.ITestContext;

import com.applitools.eyes.selenium.ClassicRunner;
import com.applitools.eyes.selenium.Eyes;
import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.gilead.config.FrameworkAssertion;
import com.gilead.config.FrameworkException;
import com.gilead.maintenance.ALMFunctions;
import com.gilead.maintenance.CRAFTLiteTestCase;
import com.gilead.maintenance.CraftDataTable;
import com.gilead.maintenance.DriverManager;
import com.gilead.maintenance.ExcelDataAccess;
import com.gilead.maintenance.IterationOptions;
import com.gilead.maintenance.OnError;
import com.gilead.maintenance.RemoteWebDriverUtils;
import com.gilead.maintenance.Settings;
import com.gilead.maintenance.WebDriverFactory;
import com.gilead.maintenance.WebDriverUtil;
import com.gilead.maintenance.WhitelistingPath;
import com.gilead.reports.APIReusuableLibrary;
import com.gilead.reports.Browser;
import com.gilead.reports.CraftDriver;
import com.gilead.reports.ExecutionMode;
import com.gilead.reports.FrameworkParameters;
import com.gilead.reports.MobileExecutionPlatform;
import com.gilead.reports.ObjectMachinate;
import com.gilead.reports.ReportSettings;
import com.gilead.reports.ReportTheme;
import com.gilead.reports.ReportThemeFactory;
import com.gilead.reports.ReportThemeFactory.Theme;
import com.gilead.reports.ScriptHelper;
import com.gilead.reports.SeleniumReport;
import com.gilead.reports.SeleniumTestParameters;
import com.gilead.reports.Status;
import com.gilead.reports.TestCaseBean;
import com.gilead.reports.ToolName;
import com.gilead.utils.ServiceRegister;
import com.gilead.utils.Util;

@SuppressWarnings("unused")
public class DriverScript {

	private List<String> businessFlowData;
	private int currentIteration, currentSubIteration;
	private Date startTime, endTime;
	private String executionTime;

	private CraftDataTable dataTable;
	private ReportSettings reportSettings;
	private SeleniumReport report;
	private CRAFTLiteTestCase testCase;

	

	private CraftDriver driver;

	private WebDriverUtil driverUtil;
	private ScriptHelper scriptHelper;

	private Properties properties;
	private Properties mobileProperties;
	private final FrameworkParameters frameworkParameters = FrameworkParameters.getInstance();

	private Boolean linkScreenshotsToTestLog = true;

	private final SeleniumTestParameters testParameters;
	private String reportPath;
	public ALMFunctions ALMFunctions;
	private APIReusuableLibrary apiDriver = new APIReusuableLibrary();
	public ExtentReports extentReport;
	public ExtentTest extentTest;
	public Map<String, String> reusableHandle = new HashMap<>();

	private ObjectMachinate objectHandling;
	private WebDriver pureWebDriver;

	private ServiceRegister register = ServiceRegister.getInstance();
	
	private String targetDatasheetDirPath, targetDataSheet;
	//private static final Logger logger = LogManager.getLogger("loggers");
	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());

	private RunContext runContext;
	private DriverManager driverManager;
	public Eyes eyes;
	public ClassicRunner runner;

	public DriverScript(SeleniumTestParameters testParameters) {
		this.testParameters = testParameters;
		this.extentReport = testParameters.getExtentReport();
		this.extentTest = testParameters.getExtentTest();
		this.driverManager = new DriverManager(testParameters);
	}

	public DriverScript(RunContext context) {
		this.runContext = context;
		this.testParameters = this.runContext.getSeleniumTestParameters();
		this.extentReport = this.runContext.getSeleniumTestParameters().getExtentReport();
		this.extentTest = this.runContext.getSeleniumTestParameters().getExtentTest();
		this.driverManager = this.runContext.getSeleniumTestParameters().getDriverManager();
	}

	public String getReportName() {
		return reportSettings.getReportName();
	}

	public void setLinkScreenshotsToTestLog(Boolean linkScreenshotsToTestLog) {
		this.linkScreenshotsToTestLog = linkScreenshotsToTestLog;
	}

	public String getTestStatus() {
		return report.getTestStatus();
	}

	public String getFailureDescription() {
		return report.getFailureDescription();
	}

	public String getExecutionTime() {
		return executionTime;
	}

	public TestCaseBean getTestCaseBean() {
		return report.getTestCaseBean();
	}

	private void startUp() {
		startTime = Util.getCurrentTime();

		properties = Settings.getInstance();
		mobileProperties = Settings.getMobilePropertiesInstance();

		setDefaultTestParameters();
		setExtentTestcase();
	}

	private void setExtentTestcase() {
		extentTest = extentReport.createTest(
				testParameters.getCurrentTestcase() + "-" + testParameters.getCurrentTestInstance(),
				testParameters.getCurrentTestDescription());
		runContext.setExtendTest(extentTest);
	}

	private void setDefaultTestParameters() {

		if (runContext.getSeleniumTestParameters().getIterationMode() == null) {
			runContext.getSeleniumTestParameters().setIterationMode(IterationOptions.RUN_ALL_ITERATIONS);
		}

		if (runContext.getSeleniumTestParameters().getExecutionMode() == null) {
			runContext.getSeleniumTestParameters()
					.setExecutionMode(ExecutionMode.valueOf(properties.getProperty("DefaultExecutionMode")));
		}

		if (runContext.getSeleniumTestParameters().getMobileExecutionPlatform() == null) {
			runContext.getSeleniumTestParameters().setMobileExecutionPlatform(
					MobileExecutionPlatform.valueOf(mobileProperties.getProperty("DefaultMobileExecutionPlatform")));
		}

		if (runContext.getSeleniumTestParameters().getMobileToolName() == null) {
			runContext.getSeleniumTestParameters()
					.setMobileToolName(ToolName.valueOf(mobileProperties.getProperty("DefaultMobileToolName")));
		}

		if (runContext.getSeleniumTestParameters().getDeviceName() == null) {
			runContext.getSeleniumTestParameters().setDeviceName(mobileProperties.getProperty("DefaultDevice"));
		}

		if (runContext.getSeleniumTestParameters().getBrowser() == null) {
			runContext.getSeleniumTestParameters()
					.setBrowser(Browser.valueOf(properties.getProperty("DefaultBrowser")));
		}

		if (runContext.getSeleniumTestParameters().getPlatform() == null) {
			runContext.getSeleniumTestParameters()
					.setPlatform(Platform.valueOf(properties.getProperty("DefaultPlatform")));
		}

		if (runContext.getSeleniumTestParameters().getSeeTestPort() == null) {
			runContext.getSeleniumTestParameters().setSeeTestPort(mobileProperties.getProperty("SeeTestDefaultPort"));
		}

		runContext.getSeleniumTestParameters().setInstallApplication(
				Boolean.parseBoolean(mobileProperties.getProperty("InstallApplicationInDevice")));
	}

	private void initializeTestIterations() throws FrameworkException {
		switch (runContext.getSeleniumTestParameters().getIterationMode()) {
		case RUN_ALL_ITERATIONS:
			int nIterations = getNumberOfIterations();
			testParameters.setEndIteration(nIterations);
			if (testParameters.getStartIteration() == 0) {
				currentIteration = 1;
			} else {
				currentIteration = testParameters.getStartIteration();
			}
			break;

		case RUN_ONE_ITERATION_ONLY:
			if (testParameters.getStartIteration() == 0) {
				currentIteration = 1;
			} else {
				currentIteration = testParameters.getStartIteration();
			}
			break;

		case RUN_RANGE_OF_ITERATIONS:
			if (runContext.getSeleniumTestParameters().getStartIteration() > runContext.getSeleniumTestParameters()
					.getEndIteration()) {
				throw new FrameworkException("Error", "StartIteration cannot be greater than EndIteration!");
			}
			currentIteration = runContext.getSeleniumTestParameters().getStartIteration();
			break;

		default:
			throw new FrameworkException("Unhandled Iteration Mode!");
		}
	}

	private int getNumberOfIterations() {

//		String encryptedDatatablePath = WhitelistingPath.cleanStringForFilePath(
//				frameworkParameters.getRelativePath() + Util.getFileSeparator() + "src" + Util.getFileSeparator()
//						+ "test" + Util.getFileSeparator() + "resources" + Util.getFileSeparator() + "Datatables");
//
//		String datatablePath = encryptedDatatablePath;
//		ExcelDataAccess testDataAccess = new ExcelDataAccess(datatablePath, testParameters.getCurrentScenario());

		ExcelDataAccess testDataAccess = new ExcelDataAccess(targetDatasheetDirPath,
				Thread.currentThread().getName() + targetDataSheet);
		runContext.setExcelDataAccess(testDataAccess);
		runContext.getExcelDataAccess().setDatasheetName(properties.getProperty("DefaultDataSheet"));

		if (properties.getProperty("Approach").equalsIgnoreCase("KeywordDriven")) {
			int startRowNum = runContext.getExcelDataAccess().getRowNum(testParameters.getCurrentTestcase(), 0);
			int nTestcaseRows = runContext.getExcelDataAccess().getRowCount(testParameters.getCurrentTestcase(), 0,
					startRowNum);
			int nSubIterations = runContext.getExcelDataAccess().getRowCount("1", 1, startRowNum); // Assumption:
			// Every
			// test
			// case
			// will
			// have
			// at
			// least
			// one
			// iteration
			return nTestcaseRows / nSubIterations;
		} else {
			return runContext.getExcelDataAccess()
					.getRowCount(runContext.getSeleniumTestParameters().getCurrentTestcase(), 0);
		}

	}

	private void maximizeWindow() {
		driver.manage().window().maximize();
	}

	public void initializeALMFunctions() {
		ALMFunctions = new ALMFunctions(runContext);
		runContext.setALMFunctions(ALMFunctions);
	}

	/**
	 * NOT USING THIS METHOD
	 * 
	 * @throws FrameworkException
	 */
	private void executeCraftOrCraftLite() throws FrameworkException {

		if (properties.getProperty("Approach").equalsIgnoreCase("KeywordDriven")) {
			executeCraft();
		} else {
			initializeTestCase();
			try {
				testCase.setUp();
				executeCRAFTLiteTestIterations();
			} catch (Exception ex) {
				exceptionHandler(ex, "Error");
			} finally {
				testCase.tearDown(); // tearDown will ALWAYS be called
			}
		}
	}

	public void driveTestExecution(ITestContext context) throws FrameworkException {

		startUp();
		driverManager = new DriverManager(runContext);
		driverManager.initializeWebDriver();
		driver = driverManager.getDriver();
		//initializeWebDriver(context);
		initializeTestReport();
		initializeDatatable();
		initializeTestIterations();
		initializeALMFunctions();
		initializeApplitools();
		initializeTestScript();

	}
	
	
	public synchronized void quitWebDriver() throws FrameworkException {

		switch (testParameters.getExecutionMode()) {
		case API:
			break;
		case LOCAL:
		case GRID:
		case MOBILE:
		case SAUCELABS:
		case TESTOBJECT:
		case PERFECTO:
		case BROWSERSTACK:
		case FASTEST:
//			CommonFunctions.windowName.clear();
			driver.quit();
			break;
		default:
			logger.error("Unhandled Execution Mode!");
			throw new FrameworkException("Unhandled Execution Mode!");
		}

	}

	public synchronized void wrapUp() {
		endTime = Util.getCurrentTime();
		closeTestReport();
		quitApplitools();
		quitWebDriver();
	}

	private void closeTestReport() {
		executionTime = Util.getTimeDifference(startTime, endTime);
		report.addTestLogFooter(executionTime);

		if (reportSettings.shouldConsolidateScreenshotsInWordDoc()) {
			report.consolidateScreenshotsInWordDoc();
		}
	}

	
	
	private void downloadAddtionalReport() {
		if (testParameters.getExecutionMode().equals(ExecutionMode.PERFECTO)
				&& reportSettings.shouldGeneratePerfectoReports()
				&& testParameters.getMobileToolName().equals(ToolName.DEFAULT)) {
			try {
				driver.close();
				String encryptedPdfReportPath = WhitelistingPath.cleanStringForFilePath(
						reportPath + Util.getFileSeparator() + "Perfecto Results" + Util.getFileSeparator()
								+ testParameters.getCurrentScenario() + "_" + testParameters.getCurrentTestcase() + "_"
								+ testParameters.getCurrentTestInstance() + ".pdf");
				RemoteWebDriverUtils.downloadReport((RemoteWebDriver) driver.getWebDriver(), "pdf",
						encryptedPdfReportPath);
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * Function to set configuration and initiate Applitools Eyes 
	 * 
	 * @param strTestMethod - name of the test method
	 * @return - no return parameters
	 */
	
	private void initializeApplitools() throws FrameworkException {
		
		if(runContext.getSeleniumTestParameters().getApplitoolsValidation().equalsIgnoreCase("Yes")) {	
			try {				
				  runner = new ClassicRunner();
				  eyes = new Eyes(runner);
				  eyes.setConfiguration(BaseTest.config);				  
				  String strProjectName = properties.getProperty("ProjectName");		  
				  eyes.open(driver.getWebDriver(), strProjectName,
							runContext.getSeleniumTestParameters().getCurrentTestcase());	
			} catch (Exception e) {
				logger.error("Exception occured while initiating Eyes");
				throw new FrameworkException("Exception occured while initiating Eyes: "+e.getLocalizedMessage());
				
			}
		}else{
			logger.info("Eyes Not initialized since Applitools validation flag is No in Run Manager Sheet");

		}
	}
	
	
	public synchronized void quitApplitools() throws FrameworkException {
		
		// Abort Eyes if Eyes is not already closed
		try {
			if(eyes!=null) {
				logger.info("Applitools Eyes is not null");
				eyes.abortIfNotClosed();
			}else {
				logger.info("Applitools Eyes is null");
			}
		} catch (Exception e) {
			logger.error("Exception occured while Aborting Eyes");
		}
	}
	

	public void createMimeHTML() {
		Properties props = new Properties();
		Session session = Session.getInstance(props, null);
		MimeMessage message = new MimeMessage(session);
		MimeMultipart mpart = new MimeMultipart("related");
		String strMessage = report.getHTMLContent();
		// String strReport = System.getProperty("ScreenshotPath");
		String currentSuite = register.getService(Thread.currentThread().getName(), "CurrentSuite").toString();

		String strReport = register.getService(currentSuite, "ReportPath").toString();

		if (strMessage.contains("<img")) {
			String[] strMessages = StringUtils.substringsBetween(strMessage, "<img", "></img>");

			for (String strM : strMessages) {
				String strReplace = StringUtils.substringBetween(strM, "Screenshots\\", ".png'");
				strMessage = strMessage.replace(strM, "<a href=\"cid:" + strReplace + "\">" + strReplace + "</a>MHT");
			}

			strMessage = strMessage.replaceAll("<img<a", "<a target=\"_blank\" ");
			strMessage = strMessage.replaceAll("</a>MHT></img>", "</a><br><br>");
			try {

				BodyPart messageBodyPart = new MimeBodyPart();
				messageBodyPart.setContent(strMessage, "text/html");

				mpart.addBodyPart(messageBodyPart);

				File screenshotsFolder = new File(strReport + Util.getFileSeparator() + "Screenshots");
				File[] allScreenshots = screenshotsFolder.listFiles();

				for (File screenshot : allScreenshots) {
					if (screenshot.isFile()) {
						MimeBodyPart imagePart = new MimeBodyPart();
						imagePart.setHeader("Content-ID", screenshot.getName().replaceAll(".png", ""));
						imagePart.setDisposition(MimeBodyPart.INLINE);
						try {
							imagePart.attachFile(screenshot);
						} catch (IOException e) {
							// add
						}
						mpart.addBodyPart(imagePart);

					}
				}
				message.setContent(mpart);
				message.setSubject("MHTML Report " + reportSettings.getReportName());
				message.addHeader("Content-Location", "index.html");
			} catch (MessagingException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
		} else {
			try {

				BodyPart messageBodyPart = new MimeBodyPart();
				messageBodyPart.setContent(strMessage, "text/html");

				mpart.addBodyPart(messageBodyPart);
				message.setContent(mpart);
				message.setSubject("MHTML Report " + reportSettings.getReportName());
				message.addHeader("Content-Location", "index.html");
			} catch (MessagingException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
		}

		try {
			new File(strReport + Util.getFileSeparator() + "MHTML Results").mkdir();
			FileOutputStream outputStream = new FileOutputStream(
					strReport + Util.getFileSeparator() + "MHTML Results//" + reportSettings.getReportName() + ".mht");
			message.writeTo(outputStream);
			outputStream.close();
		} catch (IOException | MessagingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	private void executeCraft() throws FrameworkException {
		initializeTestScript();
		executeCRAFTTestIterations();
	}

	private void initializeTestScript() {
		WebDriverUtil tmpDriverUtil = new WebDriverUtil(runContext);
		runContext.setWebDriverUtil(tmpDriverUtil);
		driverUtil = runContext.getWebDriverUtil();
		apiDriver.setRport(report);
		scriptHelper = new ScriptHelper(dataTable, report, driver, driverManager, driverUtil, ALMFunctions, apiDriver, extentTest,
				reusableHandle, eyes);
		if (!isAPITest()) {
			driver.setRport(report);
			objectHandling = new ObjectMachinate(report, pureWebDriver, frameworkParameters, testParameters);
			driver.setObjectHandling(objectHandling);
		}

		// initializeBusinessFlow();
	}

	private void executeCRAFTTestIterations() throws FrameworkException {
		while (currentIteration <= testParameters.getEndIteration()) {
			report.addTestLogSection("Iteration: " + Integer.toString(currentIteration));
			report.setIteration(currentIteration);
			// Evaluate each test iteration for any errors
			try {
				executeTestcase(businessFlowData);
			} catch (InvocationTargetException ix) {
				exceptionHandler((Exception) ix.getCause(), "Error");
			} catch (Exception ex) {
				exceptionHandler(ex, "Error");
			}

			currentIteration++;
		}
	}

	private void executeTestcase(List<String> businessFlowData)
			throws IllegalAccessException, InvocationTargetException, ClassNotFoundException, InstantiationException {
		Map<String, Integer> keywordDirectory = new HashMap<String, Integer>();

		for (int currentKeywordNum = 0; currentKeywordNum < businessFlowData.size(); currentKeywordNum++) {
			String[] currentFlowData = businessFlowData.get(currentKeywordNum).split(",");
			String currentKeyword = currentFlowData[0];

			int nKeywordIterations;
			if (currentFlowData.length > 1) {
				nKeywordIterations = Integer.parseInt(currentFlowData[1]);
			} else {
				nKeywordIterations = 1;
			}

			for (int currentKeywordIteration = 0; currentKeywordIteration < nKeywordIterations; currentKeywordIteration++) {
				if (keywordDirectory.containsKey(currentKeyword)) {
					keywordDirectory.put(currentKeyword, keywordDirectory.get(currentKeyword) + 1);
				} else {
					keywordDirectory.put(currentKeyword, 1);
				}
				currentSubIteration = keywordDirectory.get(currentKeyword);

				dataTable.setCurrentRow(testParameters.getCurrentTestcase(), currentIteration, currentSubIteration);

				if (currentSubIteration > 1) {
					report.addTestLogSubSection(currentKeyword + " (Sub-Iteration: " + currentSubIteration + ")");
					report.setSubIteration(currentSubIteration);
				} else {
					report.addTestLogSubSection(currentKeyword);
				}

				// invokeBusinessComponent(currentKeyword);
			}
		}
	}

	/**
	 * Setting SubIteration count for Report and fillform utility
	 * 
	 * @param currentKeyword
	 * @param subIteration
	 */
	public synchronized void setKeyWordIterations(String currentKeyword, int subIteration) {
		currentSubIteration = subIteration;
		dataTable.setCurrentRow(testParameters.getCurrentTestcase(), testParameters.getStartIteration(),
				currentSubIteration);
		if (subIteration > 1) {
			report.addTestLogSubSection(currentKeyword + " (Sub-Iteration: " + subIteration + ")");
			report.setSubIteration(subIteration);
		} else {
			report.addTestLogSubSection(currentKeyword);
		}

	}

	private void initializeBusinessFlow() throws FrameworkException {
		String encryptedBusinessFlowAccess = WhitelistingPath.cleanStringForFilePath(
				frameworkParameters.getRelativePath() + Util.getFileSeparator() + "src" + Util.getFileSeparator()
						+ "test" + Util.getFileSeparator() + "resources" + Util.getFileSeparator() + "Datatables");
		ExcelDataAccess businessFlowAccess = new ExcelDataAccess(encryptedBusinessFlowAccess,
				testParameters.getCurrentScenario());
		businessFlowAccess.setDatasheetName("Business_Flow");

		int rowNum = businessFlowAccess.getRowNum(testParameters.getCurrentTestcase(), 0);
		if (rowNum == -1) {
			throw new FrameworkException("The test case \"" + testParameters.getCurrentTestcase()
					+ "\" is not found in the Business Flow sheet!");
		}

		String dataValue;
		businessFlowData = new ArrayList<String>();
		int currentColumnNum = 1;
		while (true) {
			dataValue = businessFlowAccess.getValue(rowNum, currentColumnNum);
			if ("".equals(dataValue)) {
				break;
			}
			businessFlowData.add(dataValue);
			currentColumnNum++;
		}

		if (businessFlowData.isEmpty()) {
			throw new FrameworkException(
					"No business flow found against the test case \"" + testParameters.getCurrentTestcase() + "\"");
		}
	}

	private void initializeTestCase() throws FrameworkException {
		driverUtil = new WebDriverUtil(driver, report, ALMFunctions);
		scriptHelper = new ScriptHelper(dataTable, report, driver, driverManager, driverUtil, ALMFunctions, apiDriver, extentTest,
				reusableHandle, eyes);
		driver.setRport(report);
		testCase = getTestCaseInstance();
		testCase.initialize(scriptHelper);
	}

	private void exceptionHandler(Exception ex, String exceptionName) throws FrameworkException {
		// Error reporting
		String exceptionDescription = ex.getMessage();
		if (exceptionDescription == null) {
			exceptionDescription = ex.toString();
		}

		if (ex.getCause() != null) {
			report.updateTestLog(exceptionName, exceptionDescription + " <b>Caused by: </b>" + ex.getCause(),
					Status.FAIL);
		} else {
			report.updateTestLog(exceptionName, exceptionDescription, Status.FAIL);
		}

		// Print stack trace for detailed debug information
		StringWriter stringWriter = new StringWriter();
		ex.printStackTrace(new PrintWriter(stringWriter));
		String stackTrace = stringWriter.toString();
		report.updateTestLog("Exception stack trace", stackTrace, Status.DEBUG);

		// Error response
		if (frameworkParameters.getStopExecution()) {
			report.updateTestLog("CRAFT Info", "Test execution terminated by user! All subsequent tests aborted...",
					Status.DONE);
			currentIteration = testParameters.getEndIteration();
		} else {
			OnError onError = OnError.valueOf(properties.getProperty("OnError"));
			switch (onError) {
			// Stop option is not relevant when run from QC
			case NEXT_ITERATION:
				report.updateTestLog("CRAFT Info",
						"Test case iteration terminated by user! Proceeding to next iteration (if applicable)...",
						Status.DONE);
				break;

			case NEXT_TESTCASE:
				report.updateTestLog("CRAFT Info",
						"Test case terminated by user! Proceeding to next test case (if applicable)...", Status.DONE);
				currentIteration = testParameters.getEndIteration();
				break;

			case STOP:
				frameworkParameters.setStopExecution(true);
				report.updateTestLog("CRAFT Info", "Test execution terminated by user! All subsequent tests aborted...",
						Status.DONE);
				currentIteration = testParameters.getEndIteration();
				break;

			default:
				throw new FrameworkException("Unhandled OnError option!");
			}
		}
	}

	private void executeCRAFTLiteTestIterations() throws FrameworkException {
		while (currentIteration <= testParameters.getEndIteration()) {
			report.addTestLogSection("Iteration: " + Integer.toString(currentIteration));
			// Evaluate each test iteration for any errors
			try {
				testCase.executeTest();
			} catch (Exception ex) {
				exceptionHandler(ex, "Error");
			}

			currentIteration++;
			dataTable.setCurrentRow(testParameters.getCurrentTestcase(), currentIteration, 0);
		}
	}

	private synchronized void initializeWebDriver(ITestContext context) throws FrameworkException {

		switch (runContext.getSeleniumTestParameters().getExecutionMode()) {
		case API:
			break;

		case LOCAL:
			WebDriver webDriver = WebDriverFactory.getWebDriver(runContext.getSeleniumTestParameters().getBrowser());
			CraftDriver tmpDriver = new CraftDriver(webDriver);
			runContext.setCraftDriver(tmpDriver);
			driver = runContext.getCraftDriver();
			pureWebDriver = webDriver;
			driver.setTestParameters(runContext.getSeleniumTestParameters());
			maximizeWindow();
			break;

		case GRID:
			GridServiceEvaluator gridEvalautor = new GridServiceEvaluator();
			String isAvailable = gridEvalautor.isQueueAvailable();
			if (isAvailable.equalsIgnoreCase("success")) {
				String remoteUrl = gridEvalautor.getGridReachableUrl();
				WebDriver remoteGridDriver = WebDriverFactory.getRemoteWebDriver(
						runContext.getSeleniumTestParameters().getBrowser(),
						runContext.getSeleniumTestParameters().getBrowserVersion(),
						runContext.getSeleniumTestParameters().getPlatform(), remoteUrl);

				tmpDriver = new CraftDriver(remoteGridDriver);
				runContext.setCraftDriver(tmpDriver);
				driver = runContext.getCraftDriver();
				pureWebDriver = remoteGridDriver;
				driver.setTestParameters(runContext.getSeleniumTestParameters());
				maximizeWindow();
			} else if(isAvailable.equalsIgnoreCase("failure")) {
				@SuppressWarnings("unchecked")
				List<Map<String, String>> configurationErrors = (List<Map<String, String>>) context.getAttribute("ConfigurationErrors");
				Map<String,String> queueErrors = new HashMap<String,String>();
				String tscenario = runContext.getSeleniumTestParameters().getCurrentScenario();
				String tcase = runContext.getSeleniumTestParameters().getCurrentTestcase();
				queueErrors.put("TestScenario", tscenario);
				queueErrors.put("TestCase", tcase);
				queueErrors.put("TestInstance", "");
				queueErrors.put("AdditionalDetails",
						String.format("New session request queue and grid sessions are fully occupied."));
				
				configurationErrors.add(queueErrors);
				context.setAttribute("ConfigurationErrors", configurationErrors);
				throw new FrameworkAssertion("New session request queue and grid sessions are fully occupied.");
				// add to report summary
			} else if(isAvailable.equalsIgnoreCase("error")) {
				@SuppressWarnings("unchecked")
				List<Map<String, String>> configurationErrors = (List<Map<String, String>>) context.getAttribute("ConfigurationErrors");
				Map<String,String> queueErrors = new HashMap<String,String>();
				String tscenario = runContext.getSeleniumTestParameters().getCurrentScenario();
				String tcase = runContext.getSeleniumTestParameters().getCurrentTestcase();
				queueErrors.put("TestScenario", tscenario);
				queueErrors.put("TestCase", tcase);
				queueErrors.put("TestInstance", "");
				queueErrors.put("AdditionalDetails",
						String.format("Grid down, session could not be created"));
				
				configurationErrors.add(queueErrors);
				context.setAttribute("ConfigurationErrors", configurationErrors);
				throw new FrameworkAssertion("Grid down or session could not be created");
				// add to report summary
			}

			break;

		case MOBILE:
			/*
			 * WebDriver appiumDriver =
			 * AppiumDriverFactory.getAppiumDriver(testParameters.getMobileExecutionPlatform
			 * (), testParameters.getDeviceName(), testParameters.getMobileOSVersion(),
			 * testParameters.shouldInstallApplication(),
			 * mobileProperties.getProperty("AppiumURL")); driver = new
			 * CraftDriver(appiumDriver); pureWebDriver = appiumDriver;
			 * driver.setTestParameters(testParameters);
			 */

			break;

		case PERFECTO:
			/*
			 * if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) { WebDriver
			 * appiumPerfectoDriver = PerfectoDriverFactory.getPerfectoAppiumDriver(
			 * testParameters.getMobileExecutionPlatform(), testParameters.getDeviceName(),
			 * mobileProperties.getProperty("PerfectoHost")); driver = new
			 * CraftDriver(appiumPerfectoDriver); pureWebDriver = appiumPerfectoDriver;
			 * driver.setTestParameters(testParameters);
			 * 
			 * } else if
			 * (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
			 * WebDriver remotePerfectoDriver = PerfectoDriverFactory
			 * .getPerfectoRemoteWebDriverForDesktop(testParameters); driver = new
			 * CraftDriver(remotePerfectoDriver); pureWebDriver = remotePerfectoDriver;
			 * driver.setTestParameters(testParameters); }
			 */

			break;

		case SAUCELABS:
			/*
			 * if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) {
			 * AppiumDriver appiumSauceDriver = SauceLabsDriverFactory.getSauceAppiumDriver(
			 * testParameters.getMobileExecutionPlatform(), testParameters.getDeviceName(),
			 * mobileProperties.getProperty("SauceHost"), testParameters); driver = new
			 * CraftDriver(appiumSauceDriver); pureWebDriver = appiumSauceDriver;
			 * driver.setTestParameters(testParameters);
			 * 
			 * } else if
			 * (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
			 * WebDriver remoteSauceDriver = SauceLabsDriverFactory.getSauceRemoteWebDriver(
			 * mobileProperties.getProperty("SauceHost"), testParameters.getBrowser(),
			 * testParameters.getBrowserVersion(), testParameters.getPlatform(),
			 * testParameters); driver = new CraftDriver(remoteSauceDriver); pureWebDriver =
			 * remoteSauceDriver; driver.setTestParameters(testParameters); }
			 */

			break;

		case TESTOBJECT:

			/*
			 * WebDriver testObjectAppiumDriver =
			 * SauceLabsDriverFactory.getTestObjectAppiumDriver(
			 * testParameters.getMobileExecutionPlatform(), testParameters.getDeviceName(),
			 * mobileProperties.getProperty("TestObjectHost"), testParameters); driver = new
			 * CraftDriver(testObjectAppiumDriver); pureWebDriver = testObjectAppiumDriver;
			 * driver.setTestParameters(testParameters);
			 */

			break;

		case FASTEST:
			/*
			 * if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
			 * WebDriver fastestRemoteDriver =
			 * FastestDriverFactory.getRemoteWebDriver(testParameters.getBrowser(),
			 * testParameters.getBrowserVersion(), testParameters.getPlatform(),
			 * mobileProperties.getProperty("FastestHost"),
			 * testParameters.getCurrentTestcase()); driver = new
			 * CraftDriver(fastestRemoteDriver); pureWebDriver = fastestRemoteDriver;
			 * driver.setTestParameters(testParameters); } else if
			 * (testParameters.getMobileToolName().equals(ToolName.APPIUM)) { WebDriver
			 * mintAppiumtDriver = FastestDriverFactory.getMintAppiumDriver(
			 * testParameters.getMobileExecutionPlatform(), testParameters.getDeviceName(),
			 * mobileProperties.getProperty("MintHost"),
			 * testParameters.getMobileOSVersion()); driver = new
			 * CraftDriver(mintAppiumtDriver); pureWebDriver = mintAppiumtDriver;
			 * driver.setTestParameters(testParameters); }
			 */

			break;

		case BROWSERSTACK:
			/*
			 * if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
			 * WebDriver browserstackRemoteDrivermobile = BrowserStackDriverFactory
			 * .getBrowserStackRemoteWebDriverMobile(testParameters.
			 * getMobileExecutionPlatform(), testParameters.getDeviceName(),
			 * mobileProperties.getProperty("BrowserStackHost"), testParameters); driver =
			 * new CraftDriver(browserstackRemoteDrivermobile); pureWebDriver =
			 * browserstackRemoteDrivermobile; driver.setTestParameters(testParameters);
			 * 
			 * } else if (testParameters.getMobileToolName().equals(ToolName.DEFAULT)) {
			 * WebDriver browserstackRemoteDriver =
			 * BrowserStackDriverFactory.getBrowserStackRemoteWebDriver(
			 * mobileProperties.getProperty("BrowserStackHost"),
			 * testParameters.getBrowser(), testParameters.getBrowserVersion(),
			 * testParameters.getPlatform(), testParameters);
			 * 
			 * driver = new CraftDriver(browserstackRemoteDriver); pureWebDriver =
			 * browserstackRemoteDriver; driver.setTestParameters(testParameters); }
			 */

			break;

		default:
			ALMFunctions.ThrowException("Initialize Driver", "Driver should be created",
					"Driver not created successfully", false);
			throw new FrameworkException("Unhandled Execution Mode!");
		}
	}

	private void initializeTestReport() throws FrameworkException {
		initializeReportSettings();
		ReportTheme reportTheme = ReportThemeFactory
				.getReportsTheme(Theme.valueOf(properties.getProperty("ReportsTheme")));

		SeleniumReport tmpReport = new SeleniumReport(reportSettings, reportTheme, testParameters);
		runContext.setSeleniumReport(tmpReport);
		report = runContext.getSeleniumReport();
		report.initialize();
		setReportParameters();
		report.initializeTestLog();
		createTestLogHeader();
	}

	private void setReportParameters() {
		report.setExtentTest(extentTest);
		report.setDriver(driver);
	}

	private void initializeReportSettings() {
//		if (System.getProperty("ReportPath") != null) {
//			reportPath = System.getProperty("ReportPath");
//		} else {
//			reportPath = TimeStamp.getInstance();
//		}

		String currentSuite = register.getService(Thread.currentThread().getName(), "CurrentSuite").toString();

		String reportPath = register.getService(currentSuite, "ReportPath").toString();

		reportSettings = new ReportSettings(reportPath, testParameters.getCurrentScenario() + "_"
				+ testParameters.getCurrentTestcase() + "_" + testParameters.getCurrentTestInstance());

		reportSettings.setDateFormatString(properties.getProperty("DateFormatString"));
		reportSettings.setLogLevel(Integer.parseInt(properties.getProperty("LogLevel")));
		reportSettings.setProjectName(properties.getProperty("ProjectName"));
		reportSettings.setGenerateExcelReports(Boolean.parseBoolean(properties.getProperty("ExcelReport")));
		reportSettings.setGenerateHtmlReports(Boolean.parseBoolean(properties.getProperty("HtmlReport")));
		reportSettings.setGenerateSeeTestReports(
				Boolean.parseBoolean(mobileProperties.getProperty("SeeTestReportGeneration")));
		reportSettings.setGeneratePerfectoReports(
				Boolean.parseBoolean(mobileProperties.getProperty("PerfectoReportGeneration")));
		reportSettings
				.setTakeScreenshotFailedStep(Boolean.parseBoolean(properties.getProperty("TakeScreenshotFailedStep")));
		reportSettings
				.setTakeScreenshotPassedStep(Boolean.parseBoolean(properties.getProperty("TakeScreenshotPassedStep")));
		if (isAPITest()) {
			reportSettings.setTakeScreenshotFailedStep(false);
			reportSettings.setTakeScreenshotPassedStep(false);
		}
		reportSettings.setConsolidateScreenshotsInWordDoc(
				Boolean.parseBoolean(properties.getProperty("ConsolidateScreenshotsInWordDoc")));
		reportSettings.setisMobileExecution(isMobileAutomation());
		reportSettings.setAPIAutomation(isAPITest());

		reportSettings.setLinkScreenshotsToTestLog(this.linkScreenshotsToTestLog);

		RunContext.getRunContext().setReportSettings(reportSettings);
	}

	private boolean isMobileAutomation() {
		boolean isMobileAutomation = false;
		if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) {
			isMobileAutomation = true;
		}
		return isMobileAutomation;
	}

	private boolean isAPITest() {
		boolean isAPI = false;
		if (testParameters.getExecutionMode().equals(ExecutionMode.API)) {
			isAPI = true;
		}
		return isAPI;
	}

	private void createTestLogHeader() throws FrameworkException {
		report.addTestLogHeading(reportSettings.getProjectName() + " - " + reportSettings.getReportName()
				+ " Automation Execution Results");
		report.addTestLogSubHeading("Date & Time",
				": " + Util.getFormattedTime(startTime, properties.getProperty("DateFormatString")), "Iteration Mode",
				": " + testParameters.getIterationMode());
		report.addTestLogSubHeading("Start Iteration", ": " + testParameters.getStartIteration(), "End Iteration",
				": " + testParameters.getEndIteration());

		switch (testParameters.getExecutionMode()) {
		case API:
			report.addTestLogSubHeading("Execution Mode", ": " + "API", "Execution on", ": " + "Local Machine");
			break;
		case LOCAL:
			report.addTestLogSubHeading("Browser/Platform", ": " + testParameters.getBrowserAndPlatform(),
					"Execution on", ": " + "Local Machine");
			break;

		case GRID:
//			report.addTestLogSubHeading("Browser/Platform", ": " + testParameters.getBrowserAndPlatform(),
//					"Executed on", ": " + "Grid @ " + properties.getProperty("RemoteUrl"));
			report.addTestLogSubHeading("Browser/Platform", ": " + testParameters.getBrowserAndPlatform(),
					"Executed on", ": " + "AWS");
			break;

		case MOBILE:
			report.addTestLogSubHeading("Execution Mode", ": " + testParameters.getExecutionMode(),
					"Execution Platform", ": " + testParameters.getMobileExecutionPlatform());
			report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Device Name/ID",
					": " + testParameters.getDeviceName());
			break;

		case PERFECTO:

			if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
				report.addTestLogSubHeading("Execution Mode", ": " + testParameters.getExecutionMode(),
						"Execution Platform", ": " + testParameters.getPlatform());
				report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Browser/Platform",
						": " + testParameters.getBrowserAndPlatform());
				report.addTestLogSubHeading("Executed on",
						": " + "Perfecto MobileCloud @ " + mobileProperties.getProperty("PerfectoHost"), "", "");
			} else if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) {
				report.addTestLogSubHeading("Execution Mode", ": " + testParameters.getExecutionMode(),
						"Execution Platform", ": " + testParameters.getMobileExecutionPlatform());
				report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Device Name/ID",
						": " + testParameters.getDeviceName());
				report.addTestLogSubHeading("Executed on",
						": " + "Perfecto MobileCloud @ " + mobileProperties.getProperty("PerfectoHost"),
						"Perfecto User", ": " + mobileProperties.getProperty("PerfectoUser"));
			}
			break;

		case TESTOBJECT:
			report.addTestLogSubHeading("Execution Mode", ": " + testParameters.getExecutionMode(),
					"Execution Platform", ": " + testParameters.getMobileExecutionPlatform());
			report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Device Name/ID",
					": " + testParameters.getDeviceName());
			report.addTestLogSubHeading("Executed on",
					": " + "TestObject @ " + mobileProperties.getProperty("SauceHost"), "", "");
			break;

		case SAUCELABS:
			if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
				report.addTestLogSubHeading("Execution Mode", ": " + testParameters.getExecutionMode(),
						"Execution Platform", ": " + testParameters.getPlatform());
				report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Browser/Platform",
						": " + testParameters.getBrowserAndPlatform());
				report.addTestLogSubHeading("Executed on",
						": " + "Sauce Labs @ " + mobileProperties.getProperty("SauceHost"), "", "");
			} else if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) {
				report.addTestLogSubHeading("Execution Mode", ": " + testParameters.getExecutionMode(),
						"Execution Platform", ": " + testParameters.getMobileExecutionPlatform());
				report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Device Name/ID",
						": " + testParameters.getDeviceName());
				report.addTestLogSubHeading("Executed on",
						": " + "Sauce Labs @ " + mobileProperties.getProperty("SauceHost"), "", "");
			}
			break;

		case FASTEST:

			if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
				report.addTestLogSubHeading("Execution Mode", ": " + testParameters.getExecutionMode(),
						"Execution Platform", ": " + testParameters.getMobileExecutionPlatform());
				report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Browser/Platform",
						": " + testParameters.getBrowserAndPlatform());
				report.addTestLogSubHeading("Executed on",
						": " + "Mint Cloud @ " + mobileProperties.getProperty("MintHost"), "Mint User",
						": " + mobileProperties.getProperty("MintUsername"));

			} else if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) {
				report.addTestLogSubHeading("Execution Mode", ": " + testParameters.getExecutionMode(),
						"Execution Platform", ": " + testParameters.getMobileExecutionPlatform());
				report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Device OS version",
						": " + testParameters.getMobileOSVersion());
				report.addTestLogSubHeading("Executed on",
						": " + "Mint Cloud @ " + mobileProperties.getProperty("MintHost"), "Mint User",
						": " + mobileProperties.getProperty("MintUsername"));
			}

			break;

		case BROWSERSTACK:
			if (testParameters.getMobileToolName().toString().equalsIgnoreCase("REMOTE_WEBDRIVER")) {
				report.addTestLogSubHeading("ExecutionPlatform", ": " + testParameters.getExecutionMode(),
						"Executed on", ": " + testParameters.getMobileExecutionPlatform());
				report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Device Name/ID",
						": " + testParameters.getDeviceName());
				report.addTestLogSubHeading("Executed on",
						": " + "BrowserStack @ " + mobileProperties.getProperty("BrowserStackHost"), "Browser Stack",
						": " + mobileProperties.getProperty("BrowserStackHost"));
			} else {
				report.addTestLogSubHeading("ExecutionPlatform", ": " + testParameters.getExecutionMode(),
						"Executed on", ": " + testParameters.getPlatform());
				report.addTestLogSubHeading("Tool Used", ": " + testParameters.getMobileToolName(), "Browser/Platform",
						": " + testParameters.getBrowserAndPlatform());
				report.addTestLogSubHeading("Executed on",
						": " + "BrowserStack @ " + mobileProperties.getProperty("BrowserStackHost"), "Sauce User",
						": " + mobileProperties.getProperty("BrowserStackHost"));
			}
			break;

		default:
			throw new FrameworkException("Unhandled Execution Mode!");
		}

		report.addTestLogTableHeadings();
	}

	private synchronized void initializeDatatable() throws FrameworkException {
		String currentSuite = register.getService(Thread.currentThread().getName(), "CurrentSuite").toString(); 
		String suiteExecution = properties.getProperty("SequenceSuites");
		if(properties.getProperty("DatatableCoAuthoringEnabled").equalsIgnoreCase("true")) {
			String[] cmdArr = new String[7];
			cmdArr[0] = "pwsh.exe";
			cmdArr[1] = "-File";
			cmdArr[2] = new File(System.getProperty("user.dir")).getParent()+"//GileadCore//executors//EnableCoAuthoring.ps1";
			//SharePoint base path
			cmdArr[3] = "https://gileadconnect.sharepoint.com/teams/IDMAutomation/";
			cmdArr[4] = "Shared Documents/Datatables_CoAuthoring/"+properties.getProperty("ProjectName");
			String dataTableName = register.getService(Thread.currentThread().getName(), "TempSuite").toString();
			cmdArr[5] = dataTableName.substring(0, dataTableName.lastIndexOf("_")) +".xlsx";
			cmdArr[6] = frameworkParameters.getRelativePath()+"\\src\\test\\resources\\Datatables\\";
			
			try {
				Process p = Runtime.getRuntime().exec(cmdArr);
				p.waitFor();
				p.destroy();
				if(p.exitValue()==1) {
					throw new FrameworkException(
							"Error in downloading datatable: Failed to download datatables from Sharepoint...");
				}
			} catch (Exception e) {
				throw new FrameworkException(
						"Error in downloading datatable: "+e.getMessage());
			}
		}
		if(!suiteExecution.isEmpty() && suiteExecution.contains(currentSuite)) {
			initializeDatatableForSequence();
		} else {
			initializeDatatableForParallel();
		}
	}

private synchronized void initializeDatatableForSequence() throws FrameworkException {
		
		String datatablePath = frameworkParameters.getRelativePath() + Util.getFileSeparator() + "src"
				+ Util.getFileSeparator() + "test" + Util.getFileSeparator() + "resources" + Util.getFileSeparator()
				+ "Datatables";
		
		
		String tempScenario = register.getService(Thread.currentThread().getName(), "TempSuite").toString();
		tempScenario = tempScenario.substring(0, tempScenario.lastIndexOf("_"));
		
		
		String encryptedDatatablePath = WhitelistingPath.cleanStringForFilePath(
				datatablePath + Util.getFileSeparator() + tempScenario);
		
		logger.info(String.format("Fetched encryptedDatatablePath is %s", encryptedDatatablePath));

		String runTimeDatatablePath;
		Boolean includeTestDataInReport = Boolean.parseBoolean(properties.getProperty("IncludeTestDataInReport"));
		if (includeTestDataInReport) {
			runTimeDatatablePath = reportPath + Util.getFileSeparator() + "Datatables";
			String encryptedRunTimeDatatablePath = WhitelistingPath.cleanStringForFilePath(
					runTimeDatatablePath + Util.getFileSeparator() + testParameters.getCurrentScenario() + ".xlsx");

			File runTimeDatatable = new File(encryptedRunTimeDatatablePath);
			if (!runTimeDatatable.exists()) {
				File datatable = new File(encryptedDatatablePath);

				try {
					FileUtils.copyFile(datatable, runTimeDatatable);
					logger.info(String.format("Copied file %s to %s", datatable, runTimeDatatable));
				} catch (IOException e) {
					logger.error(String.format("Not able to copy file %s to %s", datatable, runTimeDatatable));
					e.printStackTrace();
					throw new FrameworkException(
							"Error in creating run-time datatable: Copying the datatable failed...");
				}
			}

			String encryptedRunTimeCommonDatatable = WhitelistingPath
					.cleanStringForFilePath(runTimeDatatablePath + Util.getFileSeparator() + "Common Testdata.xlsx");
			File runTimeCommonDatatable = new File(encryptedRunTimeCommonDatatable);
			if (!runTimeCommonDatatable.exists()) {
				String encryptedCommonDatatable = WhitelistingPath
						.cleanStringForFilePath(datatablePath + Util.getFileSeparator() + "Common Testdata.xlsx");
				File commonDatatable = new File(encryptedCommonDatatable);

				try {
					FileUtils.copyFile(commonDatatable, runTimeCommonDatatable);
					logger.info(String.format("Copied file %s to %s", commonDatatable, runTimeCommonDatatable));
				} catch (IOException e) {
					logger.error(
							String.format("Not able to copy file %s to %s", commonDatatable, runTimeCommonDatatable));
					e.printStackTrace();
					throw new FrameworkException(
							"Error in creating run-time datatable: Copying the common datatable failed...");
				}
			}
		} else {
			runTimeDatatablePath = datatablePath;
		}
		
		CraftDataTable tmpDataTable = new CraftDataTable(runTimeDatatablePath,
				tempScenario);
		runContext.setDataTable(tmpDataTable);
		dataTable = runContext.getDataTable();

		runContext.getDataTable().setDataReferenceIdentifier(properties.getProperty("DataReferenceIdentifier"));

		// CRAFTLite Change
		if (properties.getProperty("Approach").equalsIgnoreCase("ModularDriven")) {
			// Initialize the datatable row in case test data is required during
			// the setUp()
			dataTable.setCurrentRow(testParameters.getCurrentTestcase(), currentIteration, 0);
		}
	}

	private synchronized void initializeDatatableForParallel() throws FrameworkException {

		String datatablePath = frameworkParameters.getRelativePath() + Util.getFileSeparator() + "src"
				+ Util.getFileSeparator() + "test" + Util.getFileSeparator() + "resources" + Util.getFileSeparator()
				+ "Datatables";
		String encryptedDatatablePath = WhitelistingPath.cleanStringForFilePath(
				datatablePath + Util.getFileSeparator() + testParameters.getTempSuite() + ".xlsx");
		logger.info(String.format("Fetched encryptedDatatablePath is %s", encryptedDatatablePath));

		String runTimeDatatablePath;
		Boolean includeTestDataInReport = Boolean.parseBoolean(properties.getProperty("IncludeTestDataInReport"));
		if (includeTestDataInReport) {
			runTimeDatatablePath = reportPath + Util.getFileSeparator() + "Datatables";
			String encryptedRunTimeDatatablePath = WhitelistingPath.cleanStringForFilePath(
					runTimeDatatablePath + Util.getFileSeparator() + testParameters.getCurrentScenario() + ".xlsx");

			File runTimeDatatable = new File(encryptedRunTimeDatatablePath);
			if (!runTimeDatatable.exists()) {
				File datatable = new File(encryptedDatatablePath);

				try {
					FileUtils.copyFile(datatable, runTimeDatatable);
					logger.info(String.format("Copied file %s to %s", datatable, runTimeDatatable));
				} catch (IOException e) {
					logger.error(String.format("Not able to copy file %s to %s", datatable, runTimeDatatable));
					e.printStackTrace();
					throw new FrameworkException(
							"Error in creating run-time datatable: Copying the datatable failed...");
				}
			}

			String encryptedRunTimeCommonDatatable = WhitelistingPath
					.cleanStringForFilePath(runTimeDatatablePath + Util.getFileSeparator() + "Common Testdata.xlsx");
			File runTimeCommonDatatable = new File(encryptedRunTimeCommonDatatable);
			if (!runTimeCommonDatatable.exists()) {
				String encryptedCommonDatatable = WhitelistingPath
						.cleanStringForFilePath(datatablePath + Util.getFileSeparator() + "Common Testdata.xlsx");
				File commonDatatable = new File(encryptedCommonDatatable);

				try {
					FileUtils.copyFile(commonDatatable, runTimeCommonDatatable);
					logger.info(String.format("Copied file %s to %s", commonDatatable, runTimeCommonDatatable));
				} catch (IOException e) {
					logger.error(
							String.format("Not able to copy file %s to %s", commonDatatable, runTimeCommonDatatable));
					e.printStackTrace();
					throw new FrameworkException(
							"Error in creating run-time datatable: Copying the common datatable failed...");
				}
			}
		} else {
			runTimeDatatablePath = datatablePath;
		}

		String scenario = register.getService(Thread.currentThread().getName(), "CurrentSuite").toString();
		String tempScenario = register.getService(Thread.currentThread().getName(), "TempSuite").toString();
		String dataTableForReport = register.getService(scenario, "ReportPath").toString();
		if((!scenario.equalsIgnoreCase("Surefire suite")) && (!scenario.equalsIgnoreCase("Default suite"))) {
			scenario = scenario.substring(0, scenario.lastIndexOf("_"));
		}		
		tempScenario = tempScenario.substring(0, tempScenario.lastIndexOf("_"));
		String sourceFile = datatablePath + File.separator + tempScenario + ".xlsx";
		String targetDataSheetDir = dataTableForReport + File.separator + "Data Sheets";
		File targetDataSheetDirFile = new File(targetDataSheetDir);
		String targetDataSheet = runContext.getReportSettings().getReportName();// + ".xls";
		String targetReportDataSheet = dataTableForReport + File.separator + "Data Sheets" + File.separator
				+ Thread.currentThread().getName() + targetDataSheet;
		String targetCommonDataPath = dataTableForReport + File.separator + "Data Sheets" + File.separator
				+ "Common Testdata";

		if (!(targetDataSheetDirFile.exists())) {
			targetDataSheetDirFile.mkdir();
		}

		try {
			logger.info(String.format("Creating data sheet %s in report path for %s", targetReportDataSheet,
					Thread.currentThread().getName()));
			FileUtils.copyFile(new File(sourceFile), new File(targetReportDataSheet + ".xlsx"));

			String sourceCommonDataPath = datatablePath + File.separator + "Common Testdata.xlsx";

			File targetCommonDataFile = new File(targetCommonDataPath);
			if (!(targetCommonDataFile.exists())) {
				FileUtils.copyFile(new File(sourceCommonDataPath), new File(targetCommonDataFile + ".xlsx"));
			}

		} catch (IOException e) {
			e.printStackTrace();
		}

		CraftDataTable tmpDataTable = new CraftDataTable(targetDataSheetDir, targetDataSheet);
		this.targetDatasheetDirPath = targetDataSheetDir;
		this.targetDataSheet = targetDataSheet;
		runContext.setDataTable(tmpDataTable);
		dataTable = runContext.getDataTable();

		runContext.getDataTable().setDataReferenceIdentifier(properties.getProperty("DataReferenceIdentifier"));

		// CRAFTLite Change
		if (properties.getProperty("Approach").equalsIgnoreCase("ModularDriven")) {
			// Initialize the datatable row in case test data is required during
			// the setUp()
			runContext.getDataTable().setCurrentRow(runContext.getSeleniumTestParameters().getCurrentTestcase(),
					currentIteration, 0);
		}

	}
	
	@SuppressWarnings("deprecation")
	private CRAFTLiteTestCase getTestCaseInstance() throws FrameworkException {
		Class<?> testScriptClass;
		try {
			testScriptClass = Class.forName(
					"testscripts." + testParameters.getCurrentScenario() + "." + testParameters.getCurrentTestcase());
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
			throw new FrameworkException("The specified test case is not found!");
		}

		try {
			return (CRAFTLiteTestCase) testScriptClass.newInstance();
		} catch (Exception e) {
			e.printStackTrace();
			throw new FrameworkException("Error while instantiating the specified test script");
		}
	}

	public ScriptHelper getScriptHelper() throws FrameworkException {
		if (scriptHelper != null) {
			return this.scriptHelper;
		} else {
			throw new FrameworkException("ScriptHelper is not initialized");
		}
	}

	
	public int getCurrentIteration() {
		return currentIteration;
	}

	public void setCurrentIteration(int currentIteration) {
		this.currentIteration = currentIteration;
	}

	public int getCurrentSubIteration() {
		return currentSubIteration;
	}

	public void setCurrentSubIteration(int currentSubIteration) {
		this.currentSubIteration = currentSubIteration;
	}

}
