package com.gilead.testscripts.GileadCommercial_Smoke;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;

public class TalkPrep extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 2)
	public void checkLinksInWebPage() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 3)
	public void verifyPDFFileDownload() throws Exception {
		try {
			objCommonFunctions.checkPDFFileDownloadLoc();
		} finally {
			checkErrors();
		}
	}
}
