package com.gilead.base;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.Platform;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.reporter.ExtentHtmlReporter;
import com.aventstack.extentreports.reporter.KlovReporter;
import com.aventstack.extentreports.reporter.configuration.ChartLocation;
import com.aventstack.extentreports.reporter.configuration.Theme;
import com.gilead.maintenance.ExcelDataAccessforxlsm;
import com.gilead.maintenance.IterationOptions;
import com.gilead.maintenance.ResultSummaryManager;
import com.gilead.maintenance.Settings;
import com.gilead.maintenance.TestExecutionInfo;
import com.gilead.reports.Browser;
import com.gilead.reports.ExecutionMode;
import com.gilead.reports.FrameworkParameters;
import com.gilead.reports.SeleniumTestParameters;
import com.gilead.utils.Util;

/**
 * 
 * Class to get each test configuration details in run manager 
 *
 */
@SuppressWarnings("unused")
public class TestConfigurationInfo {

	private static ExtentHtmlReporter htmlReporter;
	private static ExtentReports extentReport;
	private static ExtentTest extentTest;
	private static KlovReporter klovReporter = new KlovReporter();
	Properties properties; // = Settings.getInstance();
	private Map<String, String> configErrors = new HashMap<String, String>();

	private ResultSummaryManager resultSummaryManager = ResultSummaryManager.getInstance();

	//private static final Logger logger = LogManager.getLogger("loggers");
	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());

	/**
	 * method to get configuration details of passed test case in Run manager
	 * @param testScenario
	 * @param testCase
	 * @param pkgName
	 * @param sheetName
	 * @return
	 */
	public SeleniumTestParameters getRunInfo(String testScenario, String testCase, String pkgName, String sheetName) {
		FrameworkParameters frameworkParameters = FrameworkParameters.getInstance();
		properties = Settings.getInstance();
		generateExtentReports();
		ExcelDataAccessforxlsm runManagerAccess = new ExcelDataAccessforxlsm(
				frameworkParameters.getRelativePath() + Util.getFileSeparator() + "src" + Util.getFileSeparator()
						+ "test" + Util.getFileSeparator() + "resources",
				"Run Manager");
		runManagerAccess.setDatasheetName(sheetName);
		int testScenarioRow = -1;
		if(testScenario.equalsIgnoreCase("Surefire suite") || testScenario.equalsIgnoreCase("Default suite")){
			testScenarioRow = 1;
		}
		else {
			testScenarioRow = runManagerAccess.getRowNum(testScenario, 0, 1);
		}		
		if (testScenarioRow == -1) {
			configErrors.put("TestScenario", testScenario.substring(0, testScenario.lastIndexOf("_")));
			configErrors.put("TestCase", testCase);
			configErrors.put("TestInstance", "");
			configErrors.put("AdditionalDetails",
					String.format("Given scenario %s is not existing in data sheet %s", testScenario, sheetName));
			logger.error(String.format("Given scenario %s is not existing in data sheet %s", testScenario, sheetName));

			return null;
		} else {
			logger.info(String.format("Fetched test scenario %s in data sheet %s at row number %s", testScenario,
					sheetName, testScenarioRow));
		}

		int testCaseRow = runManagerAccess.getRowNum(testCase, 1, testScenarioRow);
		if (testCaseRow == -1 ) {
			configErrors.put("TestScenario", testScenario.substring(0, testScenario.lastIndexOf("_")));
			configErrors.put("TestCase", testCase);
			configErrors.put("TestInstance", "");
			configErrors.put("AdditionalDetails", String.format(
					"Given test case %s is not existing in %s of data sheet %s", testCase, testScenario, sheetName));
			logger.error(String.format("Given test case %s is not existing in %s of data sheet %s", testCase,
					testScenario, sheetName));

			return null;
		}
			
		String tcPackage = runManagerAccess.getValue(testCaseRow, "Package");
		
		if (testCaseRow == -1 && (!tcPackage.equalsIgnoreCase(pkgName))) {
			configErrors.put("TestScenario", testScenario.substring(0, testScenario.lastIndexOf("_")));
			configErrors.put("TestCase", testCase);
			configErrors.put("TestInstance", "");
			configErrors.put("AdditionalDetails", String.format(
					"Given test case %s is not existing in %s of data sheet %s", testCase, testScenario, sheetName));
			logger.error(String.format("Given test case %s is not existing in %s of data sheet %s", testCase,
					testScenario, sheetName));

			return null;
		} else if(testCaseRow != -1 && tcPackage.equalsIgnoreCase(pkgName)){
			
			logger.info(String.format("Fetched test case %s in %s of data sheet %s at row number %s", testCase,
					pkgName, sheetName, testCaseRow));
		}

		String instanceId = runManagerAccess.getValue(testCaseRow, "TestInstance");
		int testInstanceRow = runManagerAccess.getRowNum(instanceId, 2, testCaseRow);
		if (testInstanceRow == -1) {
			configErrors.put("TestScenario", testScenario.substring(0, testScenario.lastIndexOf("_")));
			configErrors.put("TestCase", testCase);
			configErrors.put("TestInstance", instanceId);
			configErrors.put("AdditionalDetails",
					String.format("Given test instance %s is not existing for test case %s in data sheet %s",
							testInstanceRow, testCase, sheetName));
			logger.error(String.format("Given test instance %s is not existing for test case %s in data sheet %s",
					testInstanceRow, testCase, sheetName));

			return null;
		} else {
			logger.info(String.format("Fetched test instance %s in data sheet %s at row number %s", instanceId,
					sheetName, testInstanceRow));
		}

		String[] keys = { "Execute", "TestScenario", "TestCase", "TestInstance", "Description", "IterationMode",
				"StartIteration", "EndIteration", "TestConfigurationID", "Package", "RemoteGrid", "TempSuite",
				"ApplitoolsValidation", "DesktopAppIdentifier"};

		Map<String, String> values = runManagerAccess.getValuesForSpecificRow(keys, testInstanceRow);

		String currentScenario = values.get("TestScenario");
		String currentTestcase = values.get("TestCase");
		if (currentScenario.contains("Suite")) {
			currentScenario = currentScenario.substring(0, currentScenario.lastIndexOf("_"));
		}

		SeleniumTestParameters testParameters = new SeleniumTestParameters(currentScenario, currentTestcase);
		testParameters.setCurrentTestDescription(values.get("Description"));
		testParameters.setCurrentTestInstance("Instance" + values.get("TestInstance"));

		String iterationMode = values.get("IterationMode");
		if (!iterationMode.equals("")) {
			testParameters.setIterationMode(IterationOptions.valueOf(iterationMode));
		} else {
			testParameters.setIterationMode(IterationOptions.RUN_ALL_ITERATIONS);
		}

		String startIteration = values.get("StartIteration");
		if (!startIteration.equals("")) {
			testParameters.setStartIteration(Integer.parseInt(startIteration));
		}
		String endIteration = values.get("EndIteration");
		if (!endIteration.equals("")) {
			testParameters.setEndIteration(Integer.parseInt(endIteration));
		}
		String testConfig = values.get("TestConfigurationID");
		if (!"".equals(testConfig)) {
			getTestConfigValues(runManagerAccess, "TestConfigurations", testConfig, testParameters);
		}
		
		String packageName = values.get("Package");
		if(!"".equals(packageName)) {
			testParameters.setTestCasePackage(packageName);
		}
		
		String remoteGrid = values.get("RemoteGrid");
		if(!"".equals(remoteGrid)) {
			testParameters.setRemoteGrid(remoteGrid);
		}
		
		String tempSuite = values.get("TempSuite");
		if(!"".equals(tempSuite)) {
			testParameters.setTempSuite(tempSuite);
		}

		String applitoolsValidation = values.get("ApplitoolsValidation");
		if (!applitoolsValidation.equals("")) {
			testParameters.setApplitoolsValidation(applitoolsValidation);
		}
		
		String desktopAppIdentifier = values.get("DesktopAppIdentifier");
		if (!desktopAppIdentifier.equals("")) {
			testParameters.setDesktopAppIdentifier(desktopAppIdentifier);
		}
		
		// testInstancesToRun.add(testParameters);
		runManagerAccess.setDatasheetName(sheetName);
		testParameters.setExtentReport(extentReport);
		testParameters.setExtentTest(extentTest);
		return testParameters;
	}
	
	

	/**
	 * @see getRunInfo method
	 * method to store configuration data sheet data in run manager
	 * @param runManagerAccess
	 * @param sheetName
	 * @param testConfigName
	 * @param testParameters
	 */
	private void getTestConfigValues(ExcelDataAccessforxlsm runManagerAccess, String sheetName, String testConfigName,
			SeleniumTestParameters testParameters) {
		Properties properties = Settings.getInstance();

		runManagerAccess.setDatasheetName(sheetName);
		int rowNum = runManagerAccess.getRowNum(testConfigName, 0, 1);

		String[] keys = { "TestConfigurationID", "ExecutionMode", "ToolName", "MobileExecutionPlatform",
				"MobileOSVersion", "DeviceName", "Browser", "BrowserVersion", "Platform", "PlatformVersion",
				"SeeTestPort" };
		Map<String, String> values = runManagerAccess.getValuesForSpecificRow(keys, rowNum);

		String executionMode = values.get("ExecutionMode");
		if (!"".equals(executionMode)) {
			testParameters.setExecutionMode(ExecutionMode.valueOf(executionMode));
		} else {
			testParameters.setExecutionMode(ExecutionMode.valueOf(properties.getProperty("DefaultExecutionMode")));
		}

		String browser = values.get("Browser");
		if (!"".equals(browser)) {
			testParameters.setBrowser(Browser.valueOf(browser));
		} else {
			testParameters.setBrowser(Browser.valueOf(properties.getProperty("DefaultBrowser")));
		}

		String browserVersion = values.get("BrowserVersion");
		if (!"".equals(browserVersion)) {
			testParameters.setBrowserVersion(browserVersion);
		}

		String platform = values.get("Platform");
		if (!"".equals(platform)) {
			testParameters.setPlatform(Platform.valueOf(platform));
		} else {
			testParameters.setPlatform(Platform.valueOf(properties.getProperty("DefaultPlatform")));
		}

		String platformVersion = values.get("PlatformVersion");
		if (!"".equals(platformVersion)) {
			testParameters.setPlatformVersion(platformVersion);
		}
	}

	/**
	 * @see TestJob class
	 * method to get List<TestExecutionInfo> details Run manager 
	 * @param sheetName
	 * @return
	 */
	public List<TestExecutionInfo> getTestExecutionsInfo(String sheetName) {
		FrameworkParameters frameworkParameters = FrameworkParameters.getInstance();
		ResultSummaryManager.getInstance().setRelativePath();

		ExcelDataAccessforxlsm runManagerAccess = new ExcelDataAccessforxlsm(
				frameworkParameters.getRelativePath() + Util.getFileSeparator() + "src" + Util.getFileSeparator()
						+ "test" + Util.getFileSeparator() + "resources",
				"Run Manager");
		runManagerAccess.setDatasheetName(sheetName);

		List<TestExecutionInfo> executionsInfo = new ArrayList<TestExecutionInfo>();
		String[] keys = { "Execute", "TestScenario", "TestCase", "TestInstance", "Description", "IterationMode",
				"StartIteration", "EndIteration", "TestConfigurationID", "Package", "RemoteGrid", "TempSuite", 
				"ApplitoolsValidation"};
		List<Map<String, String>> values = runManagerAccess.getValues(keys);

		for (int currentTestInstance = 0; currentTestInstance < values.size(); currentTestInstance++) {

			Map<String, String> row = values.get(currentTestInstance);
			String executeFlag = row.get("Execute");

			if (executeFlag.equalsIgnoreCase("Yes")) {
				
				TestExecutionInfo testExecutionInfo = new TestExecutionInfo();
				testExecutionInfo.setTestScenario(row.get("TestScenario"));
				testExecutionInfo.setTestCase(row.get("TestCase"));
				testExecutionInfo.setDescription(row.get("Description"));
				testExecutionInfo.setTestInstance("Instance" + row.get("TestInstance"));

				String iterationMode = row.get("IterationMode");
				if (!iterationMode.equals("")) {
					testExecutionInfo.setIterationMode(IterationOptions.valueOf(iterationMode));
				} else {
					testExecutionInfo.setIterationMode(IterationOptions.RUN_ALL_ITERATIONS);
				}

				String startIteration = row.get("StartIteration");
				if (!startIteration.equals("")) {
					testExecutionInfo.setStartIteration(Integer.parseInt(startIteration));
				}
				String endIteration = row.get("EndIteration");
				if (!endIteration.equals("")) {
					testExecutionInfo.setEndIteration(Integer.parseInt(endIteration));
				}
				String testConfig = row.get("TestConfigurationID");
				if (!"".equals(testConfig)) {
					getTestConfigValues(runManagerAccess, "TestConfigurations", testConfig, testExecutionInfo);
				}
				
				String packageName = row.get("Package");
				if(!"".equals(packageName)) {
					testExecutionInfo.setTestCasePackage(packageName);
				}

				String remoteGrid= row.get("RemoteGrid");
				if(!"".equals(remoteGrid)) {
					testExecutionInfo.setRemoteGrid(remoteGrid);
				}
				
				String tempSuite = row.get("TempSuite");
				if(!"".equals(tempSuite)) {
					testExecutionInfo.setTempSuite(tempSuite);
				}
				
				String applitoolsValidation = row.get("ApplitoolsValidation");
				if(!"".equals(applitoolsValidation)) {
					testExecutionInfo.setApplitoolsValidation(applitoolsValidation);
				}
				
				executionsInfo.add(testExecutionInfo);
				runManagerAccess.setDatasheetName(sheetName);
			}
		}
		return executionsInfo;
	}
	
	/**
	 * @see TestJob class
	 * @param sheetName
	 * @param testParameters
	 * @return
	 */
	public List<TestExecutionInfo> getTestExecutionsInfo(String sheetName,
			List<SeleniumTestParameters> testParameters) {
		FrameworkParameters frameworkParameters = FrameworkParameters.getInstance();
		ResultSummaryManager.getInstance().setRelativePath();

		ExcelDataAccessforxlsm runManagerAccess = new ExcelDataAccessforxlsm(
				frameworkParameters.getRelativePath() + Util.getFileSeparator() + "src" + Util.getFileSeparator()
						+ "test" + Util.getFileSeparator() + "resources",
				"Run Manager");
		runManagerAccess.setDatasheetName(sheetName);

		List<TestExecutionInfo> executionsInfo = new ArrayList<TestExecutionInfo>();
		String[] keys = { "Execute", "TestScenario", "TestCase", "TestInstance", "Description", "IterationMode",
				"StartIteration", "EndIteration", "TestConfigurationID", "Package", "RemoteGrid", "TempSuite",
				"ApplitoolsValidation"};
		List<Map<String, String>> values = runManagerAccess.getValues(keys);

		for (int currentTestInstance = 0; currentTestInstance < testParameters.size(); currentTestInstance++) {

			SeleniumTestParameters row = testParameters.get(currentTestInstance);
			TestExecutionInfo testExecutionInfo = new TestExecutionInfo();
			testExecutionInfo.setTestScenario(row.getCurrentScenario());
			testExecutionInfo.setTestCase(row.getCurrentTestcase());
			testExecutionInfo.setDescription(row.getCurrentTestDescription());
			testExecutionInfo.setTestInstance(row.getCurrentTestInstance());

			testExecutionInfo.setIterationMode(row.getIterationMode());
			testExecutionInfo.setStartIteration(row.getStartIteration());
			testExecutionInfo.setEndIteration(row.getEndIteration());
			testExecutionInfo.setExecutionMode(row.getExecutionMode());
			testExecutionInfo.setBrowser(row.getBrowser());
			testExecutionInfo.setBrowserVersion(row.getBrowserVersion());
			testExecutionInfo.setPlatform(row.getPlatform());
			testExecutionInfo.setPlatformVersion(row.getPlatformVersion());
			testExecutionInfo.setTestCasePackage(row.getTestCasePackage());
			testExecutionInfo.setRemoteGrid(row.getRemoteGrid());
			testExecutionInfo.setTempSuite(row.getTempSuite());			
			// Applitools
			testExecutionInfo.setApplitoolsValidation(row.getApplitoolsValidation());
			
			executionsInfo.add(testExecutionInfo);
			runManagerAccess.setDatasheetName(sheetName);
		}
		return executionsInfo;
	}

	/**
	 * method to get configuration details
	 * @param runManagerAccess
	 * @param sheetName
	 * @param testConfigName
	 * @param testExecutionInfo
	 */
	private void getTestConfigValues(ExcelDataAccessforxlsm runManagerAccess, String sheetName, String testConfigName,
			TestExecutionInfo testExecutionInfo) {

		runManagerAccess.setDatasheetName(sheetName);
		int rowNum = runManagerAccess.getRowNum(testConfigName, 0, 1);

		String[] keys = { "TestConfigurationID", "ExecutionMode", "ToolName", "MobileExecutionPlatform",
				"MobileOSVersion", "DeviceName", "Browser", "BrowserVersion", "Platform", "PlatformVersion",
				"SeeTestPort" };
		Map<String, String> values = runManagerAccess.getValuesForSpecificRow(keys, rowNum);

		String executionMode = values.get("ExecutionMode");
		if (!"".equals(executionMode)) {
			testExecutionInfo.setExecutionMode(ExecutionMode.valueOf(executionMode));
		} else {
			testExecutionInfo.setExecutionMode(ExecutionMode.valueOf(properties.getProperty("DefaultExecutionMode")));
		}

		String browser = values.get("Browser");
		if (!"".equals(browser)) {
			testExecutionInfo.setBrowser(Browser.valueOf(browser));
		} else {
			testExecutionInfo.setBrowser(Browser.valueOf(properties.getProperty("DefaultBrowser")));
		}

		String browserVersion = values.get("BrowserVersion");
		if (!"".equals(browserVersion)) {
			testExecutionInfo.setBrowserVersion(browserVersion);
		}

		String platform = values.get("Platform");
		if (!"".equals(platform)) {
			testExecutionInfo.setPlatform(Platform.valueOf(platform));
		} else {
			testExecutionInfo.setPlatform(Platform.valueOf(properties.getProperty("DefaultPlatform")));
		}

		String platformVersion = values.get("PlatformVersion");
		if (!"".equals(platformVersion)) {
			testExecutionInfo.setPlatformVersion(platformVersion);
		}
	}

	private void generateExtentReports() {
		integrateWithKlov();
		htmlReporter = new ExtentHtmlReporter(resultSummaryManager.getReportPath() + Util.getFileSeparator()
				+ "Extent Result" + Util.getFileSeparator() + "ExtentReport.html");
		extentReport = new ExtentReports();
		extentReport.attachReporter(htmlReporter);
		extentReport.setSystemInfo("Project Name", properties.getProperty("ProjectName"));
		extentReport.setSystemInfo("Framework", "CRAFT Maven");
		extentReport.setSystemInfo("Framework Version", "3.2");
		extentReport.setSystemInfo("Author", "Cognizant");

		htmlReporter.config().setDocumentTitle("CRAFT Extent Report");
		htmlReporter.config().setReportName("Extent Report for CRAFT");
		htmlReporter.config().setTestViewChartLocation(ChartLocation.TOP);
		htmlReporter.config().setTheme(Theme.STANDARD);
	}

	private void integrateWithKlov() {
		String dbHost = properties.getProperty("DBHost");
		String dbPort = properties.getProperty("DBPort");
		if (Boolean.parseBoolean(properties.getProperty("GenerateKlov"))) {
			klovReporter.initMongoDbConnection(dbHost, Integer.valueOf(dbPort));
			klovReporter.setProjectName(properties.getProperty("GenerateKlov"));
			klovReporter.setReportName("CRAFT Reports");
			klovReporter.setKlovUrl(properties.getProperty("KlovURL"));
		}
	}

	public Map<String, String> getConfigErrors() {
		return configErrors;
	}

}
